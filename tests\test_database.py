"""
Tests for user database functionality
"""
import pytest
import sqlite3
import tempfile
import os
import sys

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from user_database import UserDatabase

class TestUserDatabase:
    """Test cases for the user database"""
    
    def setup_method(self):
        """Setup test fixtures with temporary database"""
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.db = UserDatabase(self.temp_db.name)
        self.test_session_id = "test_session_123"
    
    def teardown_method(self):
        """Clean up temporary database"""
        if os.path.exists(self.temp_db.name):
            os.unlink(self.temp_db.name)
    
    def test_database_initialization(self):
        """Test that database tables are created correctly"""
        with sqlite3.connect(self.temp_db.name) as conn:
            cursor = conn.cursor()
            
            # Check that all required tables exist
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            expected_tables = [
                'user_sessions',
                'emotion_detections',
                'music_recommendations',
                'user_feedback',
                'user_preferences'
            ]
            
            for table in expected_tables:
                assert table in tables
    
    def test_create_session(self):
        """Test creating a new user session"""
        success = self.db.create_session(
            session_id=self.test_session_id,
            language="English",
            favorite_artist="Test Artist"
        )
        
        assert success is True
        
        # Verify session was created
        with sqlite3.connect(self.temp_db.name) as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT session_id, language, favorite_artist FROM user_sessions WHERE session_id = ?",
                (self.test_session_id,)
            )
            result = cursor.fetchone()
            
            assert result is not None
            assert result[0] == self.test_session_id
            assert result[1] == "English"
            assert result[2] == "Test Artist"
    
    def test_log_emotion_detection(self):
        """Test logging emotion detection events"""
        # Create session first
        self.db.create_session(self.test_session_id)
        
        success = self.db.log_emotion_detection(
            session_id=self.test_session_id,
            emotion="happy",
            confidence=0.85
        )
        
        assert success is True
        
        # Verify emotion was logged
        with sqlite3.connect(self.temp_db.name) as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT emotion, confidence FROM emotion_detections WHERE session_id = ?",
                (self.test_session_id,)
            )
            result = cursor.fetchone()
            
            assert result is not None
            assert result[0] == "happy"
            assert result[1] == 0.85
    
    def test_log_music_recommendation(self):
        """Test logging music recommendations"""
        # Create session first
        self.db.create_session(self.test_session_id)
        
        rec_id = self.db.log_music_recommendation(
            session_id=self.test_session_id,
            emotion="happy",
            track_name="Happy Song",
            artist="Happy Artist",
            source="spotify"
        )
        
        assert rec_id is not None
        assert isinstance(rec_id, int)
        
        # Verify recommendation was logged
        with sqlite3.connect(self.temp_db.name) as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT track_name, artist, source FROM music_recommendations WHERE id = ?",
                (rec_id,)
            )
            result = cursor.fetchone()
            
            assert result is not None
            assert result[0] == "Happy Song"
            assert result[1] == "Happy Artist"
            assert result[2] == "spotify"
    
    def test_add_user_feedback(self):
        """Test adding user feedback"""
        # Create session and recommendation first
        self.db.create_session(self.test_session_id)
        rec_id = self.db.log_music_recommendation(
            self.test_session_id, "happy", "Test Song", "Test Artist", "spotify"
        )
        
        success = self.db.add_user_feedback(
            session_id=self.test_session_id,
            recommendation_id=rec_id,
            emotion="happy",
            track_name="Test Song",
            artist="Test Artist",
            rating=5,
            feedback_text="Great song!"
        )
        
        assert success is True
        
        # Verify feedback was added
        with sqlite3.connect(self.temp_db.name) as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT rating, feedback_text FROM user_feedback WHERE recommendation_id = ?",
                (rec_id,)
            )
            result = cursor.fetchone()
            
            assert result is not None
            assert result[0] == 5
            assert result[1] == "Great song!"
    
    def test_get_user_preferences(self):
        """Test getting user preferences"""
        # Create session first
        self.db.create_session(self.test_session_id)
        
        # Add some feedback to generate preferences
        rec_id = self.db.log_music_recommendation(
            self.test_session_id, "happy", "Test Song", "Test Artist", "spotify"
        )
        self.db.add_user_feedback(
            self.test_session_id, rec_id, "happy", "Test Song", "Test Artist", 5
        )
        
        preferences = self.db.get_user_preferences(self.test_session_id, "happy")
        
        assert isinstance(preferences, dict)
        assert 'preferred_genres' in preferences
        assert 'preferred_artists' in preferences
        assert 'disliked_genres' in preferences
        assert isinstance(preferences['preferred_artists'], list)
    
    def test_get_emotion_statistics(self):
        """Test getting emotion statistics"""
        # Create session and add some emotion detections
        self.db.create_session(self.test_session_id)
        self.db.log_emotion_detection(self.test_session_id, "happy", 0.8)
        self.db.log_emotion_detection(self.test_session_id, "happy", 0.9)
        self.db.log_emotion_detection(self.test_session_id, "sad", 0.7)
        
        stats = self.db.get_emotion_statistics(self.test_session_id)
        
        assert stats['total_detections'] == 3
        assert stats['most_common_emotion'] == 'happy'
        assert len(stats['emotion_breakdown']) == 2  # happy and sad
        
        # Check happy emotion stats
        happy_stats = next(e for e in stats['emotion_breakdown'] if e['emotion'] == 'happy')
        assert happy_stats['count'] == 2
        assert happy_stats['avg_confidence'] == 0.85
    
    def test_get_feedback_statistics(self):
        """Test getting feedback statistics"""
        # Create session and add feedback
        self.db.create_session(self.test_session_id)
        
        # Add multiple recommendations and feedback
        for i, rating in enumerate([5, 4, 3]):
            rec_id = self.db.log_music_recommendation(
                self.test_session_id, "happy", f"Song {i}", f"Artist {i}", "spotify"
            )
            self.db.add_user_feedback(
                self.test_session_id, rec_id, "happy", f"Song {i}", f"Artist {i}", rating
            )
        
        stats = self.db.get_feedback_statistics(self.test_session_id)
        
        assert stats['overall_satisfaction'] == 4.0  # (5+4+3)/3
        assert len(stats['emotion_feedback']) == 1  # Only happy emotion
        
        happy_feedback = stats['emotion_feedback'][0]
        assert happy_feedback['emotion'] == 'happy'
        assert happy_feedback['avg_rating'] == 4.0
        assert happy_feedback['feedback_count'] == 3
    
    def test_preference_learning_from_feedback(self):
        """Test that preferences are updated based on feedback"""
        # Create session
        self.db.create_session(self.test_session_id)
        
        # Add good feedback for an artist
        rec_id = self.db.log_music_recommendation(
            self.test_session_id, "happy", "Great Song", "Favorite Artist", "spotify"
        )
        self.db.add_user_feedback(
            self.test_session_id, rec_id, "happy", "Great Song", "Favorite Artist", 5
        )
        
        # Check that artist was added to preferences
        preferences = self.db.get_user_preferences(self.test_session_id, "happy")
        assert "Favorite Artist" in preferences['preferred_artists']
        
        # Add bad feedback for another artist
        rec_id2 = self.db.log_music_recommendation(
            self.test_session_id, "happy", "Bad Song", "Disliked Artist", "spotify"
        )
        self.db.add_user_feedback(
            self.test_session_id, rec_id2, "happy", "Bad Song", "Disliked Artist", 1
        )
        
        # Check that bad artist is not in preferred list
        preferences = self.db.get_user_preferences(self.test_session_id, "happy")
        assert "Disliked Artist" not in preferences['preferred_artists']

if __name__ == '__main__':
    pytest.main([__file__])
