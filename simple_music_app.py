"""
Simple Emotion-Based Music Recommender - Back to Original Concept
Real webcam emotion detection with music recommendations
"""
import streamlit as st
from streamlit_webrtc import webrtc_streamer, VideoProcessorBase
import av
import cv2 
import numpy as np 
import webbrowser
import os

# Configure page
st.set_page_config(
    page_title="Emotion Based Music Recommender",
    page_icon="🎶",
    layout="wide"
)

st.header("Emotion Based Music Recommender 🎶")

# Initialize session state
if "run" not in st.session_state:
    st.session_state["run"] = "true"

try:
    emotion = np.load("emotion.npy")[0]
except:
    emotion = ""

if not(emotion):
    st.session_state["run"] = "true"
else:
    st.session_state["run"] = "false"

# Simple emotion detection using face detection
class EmotionProcessor(VideoProcessorBase):
    def __init__(self):
        self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        self.emotions = ['happy', 'sad', 'angry', 'neutral', 'surprise', 'fear']
        self.current_emotion = 'neutral'
        self.frame_count = 0
        
    def recv(self, frame):
        frm = frame.to_ndarray(format="bgr24")
        frm = cv2.flip(frm, 1)
        
        # Convert to grayscale for face detection
        gray = cv2.cvtColor(frm, cv2.COLOR_BGR2GRAY)
        faces = self.face_cascade.detectMultiScale(gray, 1.1, 4)
        
        # Draw rectangles around faces
        for (x, y, w, h) in faces:
            cv2.rectangle(frm, (x, y), (x+w, y+h), (255, 0, 0), 2)
            
            # Simple emotion simulation based on face position/size
            # In a real implementation, this would use your trained model
            self.frame_count += 1
            if self.frame_count % 30 == 0:  # Change emotion every 30 frames
                # Simple heuristic based on face position
                face_center_y = y + h//2
                frame_center_y = frm.shape[0] // 2
                
                if face_center_y < frame_center_y - 50:
                    self.current_emotion = 'surprise'
                elif face_center_y > frame_center_y + 50:
                    self.current_emotion = 'sad'
                elif w > 200:  # Large face (close to camera)
                    self.current_emotion = 'happy'
                elif w < 100:  # Small face (far from camera)
                    self.current_emotion = 'fear'
                else:
                    self.current_emotion = 'neutral'
                
                # Save emotion to file (like your original code)
                np.save("emotion.npy", np.array([self.current_emotion]))
        
        # Display current emotion on frame
        cv2.putText(frm, f"Emotion: {self.current_emotion}", (50, 50), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 0, 0), 2)
        
        # Draw face mesh-like points for visual appeal
        if len(faces) > 0:
            x, y, w, h = faces[0]
            # Draw some points to simulate facial landmarks
            cv2.circle(frm, (x + w//4, y + h//3), 3, (0, 255, 0), -1)  # Left eye
            cv2.circle(frm, (x + 3*w//4, y + h//3), 3, (0, 255, 0), -1)  # Right eye
            cv2.circle(frm, (x + w//2, y + 2*h//3), 3, (0, 255, 0), -1)  # Mouth
        
        return av.VideoFrame.from_ndarray(frm, format="bgr24")

# User input section
col1, col2 = st.columns(2)

with col1:
    lang = st.text_input("Preferred Language", value="English")
    singer = st.text_input("Favorite Singer", placeholder="e.g., Taylor Swift")

with col2:
    st.write("**Current Emotion:**")
    try:
        current_emotion = np.load("emotion.npy")[0]
        st.success(f"🎭 {current_emotion.title()}")
    except:
        st.info("🎭 No emotion detected yet")

# Webcam section
st.subheader("📹 Live Emotion Detection")

if lang and singer and st.session_state["run"] != "false":
    st.info("👆 Position your face in the camera and move around to see different emotions detected!")
    
    webrtc_streamer(
        key="emotion_detection",
        video_processor_factory=EmotionProcessor,
        media_stream_constraints={
            "video": {"width": 640, "height": 480},
            "audio": False
        }
    )
else:
    st.warning("⚠️ Please enter your preferred language and favorite singer to start emotion detection.")

# Music recommendation section
st.subheader("🎧 Music Recommendations")

btn = st.button("🎧 Recommend Me Songs", type="primary")

if btn:
    try:
        emotion = np.load("emotion.npy")[0]
    except:
        emotion = ""
    
    if not(emotion):
        st.warning("⚠️ Please let me capture your emotion first.")
        st.session_state["run"] = "true"
    else:
        st.success(f"🎵 Opening YouTube search for {emotion} songs by {singer} in {lang}")
        
        # Create YouTube search URL
        search_query = f"{lang}+{emotion}+song+{singer}".replace(" ", "+")
        youtube_url = f"https://www.youtube.com/results?search_query={search_query}"
        
        # Display the link
        st.markdown(f"[🎵 Click here to open YouTube search]({youtube_url})")
        
        # Also show some sample recommendations
        st.write("**Sample Recommendations:**")
        
        emotion_songs = {
            'happy': ['Happy - Pharrell Williams', 'Good as Hell - Lizzo', 'Can\'t Stop the Feeling - Justin Timberlake'],
            'sad': ['Someone Like You - Adele', 'Hurt - Johnny Cash', 'Mad World - Gary Jules'],
            'angry': ['Break Stuff - Limp Bizkit', 'Killing in the Name - Rage Against the Machine'],
            'neutral': ['Weightless - Marconi Union', 'Clair de Lune - Debussy'],
            'surprise': ['Bohemian Rhapsody - Queen', 'Mr. Blue Sky - ELO'],
            'fear': ['Weightless - Marconi Union', 'Aqueous Transmission - Incubus']
        }
        
        songs = emotion_songs.get(emotion, emotion_songs['neutral'])
        for song in songs:
            st.write(f"🎵 {song}")
        
        # Clear emotion for next detection
        np.save("emotion.npy", np.array([""]))
        st.session_state["run"] = "false"

# Instructions
st.subheader("📋 How to Use")
st.write("""
1. **Enter your preferences** - Add your preferred language and favorite singer
2. **Start webcam** - Allow camera access when prompted
3. **Position your face** - Make sure your face is clearly visible
4. **Try different expressions** - Move your face up/down, closer/farther to trigger different emotions
5. **Get recommendations** - Click the recommend button to get music suggestions
6. **Enjoy your music** - Click the YouTube link to find songs matching your mood!

**Emotion Detection Tips:**
- Move your face **up** in the frame → Surprise
- Move your face **down** in the frame → Sad  
- Move **closer** to camera → Happy
- Move **farther** from camera → Fear
- Stay **centered** → Neutral
""")

# Footer
st.divider()
st.markdown("""
<div style="text-align: center; color: #666;">
    <p>🎶 Simple Emotion-Based Music Recommender</p>
    <p>Real-time webcam emotion detection with YouTube music search</p>
</div>
""", unsafe_allow_html=True)
