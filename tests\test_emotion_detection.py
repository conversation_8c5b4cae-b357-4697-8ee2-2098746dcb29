"""
Tests for emotion detection functionality
"""
import pytest
import numpy as np
import cv2
from unittest.mock import Mock, patch
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from emotion_detector import EmotionDetector, FallbackEmotionDetector

class TestEmotionDetector:
    """Test cases for the enhanced emotion detector"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.detector = EmotionDetector()
        self.test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
    
    @patch('emotion_detector.DeepFace.analyze')
    def test_detect_emotion_success(self, mock_analyze):
        """Test successful emotion detection"""
        # Mock DeepFace response
        mock_analyze.return_value = {
            'emotion': {
                'angry': 10.0,
                'disgust': 5.0,
                'fear': 15.0,
                'happy': 60.0,
                'sad': 5.0,
                'surprise': 3.0,
                'neutral': 2.0
            },
            'dominant_emotion': 'happy'
        }
        
        result = self.detector.detect_emotion(self.test_frame)
        
        assert result['emotion'] == 'happy'
        assert result['confidence'] == 0.6
        assert result['emoji'] == '😊'
        assert 'timestamp' in result
    
    @patch('emotion_detector.DeepFace.analyze')
    def test_detect_emotion_low_confidence(self, mock_analyze):
        """Test emotion detection with low confidence"""
        # Mock low confidence response
        mock_analyze.return_value = {
            'emotion': {
                'angry': 20.0,
                'disgust': 15.0,
                'fear': 15.0,
                'happy': 25.0,
                'sad': 15.0,
                'surprise': 5.0,
                'neutral': 5.0
            },
            'dominant_emotion': 'happy'
        }
        
        result = self.detector.detect_emotion(self.test_frame)
        
        # Should default to neutral due to low confidence
        assert result['emotion'] == 'neutral'
        assert result['confidence'] == 0.5
    
    @patch('emotion_detector.DeepFace.analyze')
    def test_detect_emotion_error_handling(self, mock_analyze):
        """Test error handling in emotion detection"""
        # Mock exception
        mock_analyze.side_effect = Exception("DeepFace error")
        
        result = self.detector.detect_emotion(self.test_frame)
        
        assert result['emotion'] == 'neutral'
        assert result['confidence'] == 0.0
        assert 'error' in result
    
    def test_emotion_history_smoothing(self):
        """Test emotion history smoothing functionality"""
        # Add some test emotions to history
        self.detector._update_history('happy', 0.8)
        self.detector._update_history('happy', 0.7)
        self.detector._update_history('sad', 0.6)
        
        smoothed = self.detector._get_smoothed_emotion()
        
        # Happy should win due to higher confidence and frequency
        assert smoothed == 'happy'
    
    def test_draw_emotion_info(self):
        """Test drawing emotion information on frame"""
        emotion_data = {
            'emotion': 'happy',
            'confidence': 0.85,
            'emoji': '😊'
        }
        
        result_frame = self.detector.draw_emotion_info(self.test_frame.copy(), emotion_data)
        
        # Check that frame was modified (not equal to original)
        assert not np.array_equal(result_frame, self.test_frame)
        assert result_frame.shape == self.test_frame.shape
    
    def test_get_emotion_statistics(self):
        """Test emotion statistics generation"""
        # Add some test data
        self.detector._update_history('happy', 0.8)
        self.detector._update_history('sad', 0.6)
        self.detector._update_history('happy', 0.9)
        
        stats = self.detector.get_emotion_statistics()
        
        assert stats['total_detections'] == 3
        assert 'emotion_counts' in stats
        assert 'emotion_percentages' in stats
        assert stats['most_common'] == 'happy'

class TestFallbackEmotionDetector:
    """Test cases for the fallback emotion detector"""
    
    def setup_method(self):
        """Setup test fixtures"""
        # Mock the model loading to avoid file dependencies
        with patch('emotion_detector.load_model'), \
             patch('emotion_detector.np.load'), \
             patch('emotion_detector.mp'):
            self.detector = FallbackEmotionDetector()
            self.detector.model = Mock()
            self.detector.labels = np.array(['happy', 'sad', 'angry'])
        
        self.test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
    
    def test_detect_emotion_no_model(self):
        """Test fallback detector when model is not loaded"""
        detector = FallbackEmotionDetector()
        detector.model = None
        
        result = detector.detect_emotion(self.test_frame)
        
        assert result['emotion'] == 'neutral'
        assert result['confidence'] == 0.0
        assert 'error' in result
    
    @patch('emotion_detector.mp')
    def test_detect_emotion_with_landmarks(self, mock_mp):
        """Test emotion detection with face landmarks"""
        # Mock MediaPipe results
        mock_landmark = Mock()
        mock_landmark.x = 0.5
        mock_landmark.y = 0.5
        
        mock_face_landmarks = Mock()
        mock_face_landmarks.landmark = [mock_landmark] * 468  # Typical face landmark count
        
        mock_result = Mock()
        mock_result.face_landmarks = mock_face_landmarks
        mock_result.left_hand_landmarks = None
        mock_result.right_hand_landmarks = None
        
        self.detector.holis.process.return_value = mock_result
        
        # Mock model prediction
        self.detector.model.predict.return_value = np.array([[0.1, 0.8, 0.1]])
        
        result = self.detector.detect_emotion(self.test_frame)
        
        assert result['emotion'] == 'sad'  # Index 1 in labels array
        assert result['confidence'] == 0.8
        assert 'emoji' in result

if __name__ == '__main__':
    pytest.main([__file__])
