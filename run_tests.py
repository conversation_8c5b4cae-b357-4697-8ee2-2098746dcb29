#!/usr/bin/env python3
"""
Simple test runner to verify the enhanced emotion music recommender works correctly
"""
import sys
import os
import subprocess
import importlib.util

def check_dependencies():
    """Check if all required dependencies are available"""
    required_modules = [
        'streamlit',
        'cv2',
        'numpy',
        'deepface',
        'spotipy',
        'plotly',
        'sqlite3'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            if module == 'cv2':
                import cv2
            elif module == 'sqlite3':
                import sqlite3
            else:
                importlib.import_module(module)
            print(f"✅ {module} - OK")
        except ImportError:
            print(f"❌ {module} - MISSING")
            missing_modules.append(module)
    
    return missing_modules

def test_imports():
    """Test importing our custom modules"""
    try:
        from emotion_detector import EmotionDetector, FallbackEmotionDetector
        print("✅ emotion_detector - OK")
    except ImportError as e:
        print(f"❌ emotion_detector - FAILED: {e}")
        return False
    
    try:
        from music_recommender import MusicRecommender
        print("✅ music_recommender - OK")
    except ImportError as e:
        print(f"❌ music_recommender - FAILED: {e}")
        return False
    
    try:
        from user_database import UserDatabase
        print("✅ user_database - OK")
    except ImportError as e:
        print(f"❌ user_database - FAILED: {e}")
        return False
    
    try:
        import config
        print("✅ config - OK")
    except ImportError as e:
        print(f"❌ config - FAILED: {e}")
        return False
    
    return True

def test_basic_functionality():
    """Test basic functionality of core components"""
    print("\n🧪 Testing Basic Functionality...")
    
    try:
        # Test database
        from user_database import UserDatabase
        db = UserDatabase(':memory:')  # Use in-memory database for testing
        success = db.create_session('test_session', 'English', 'Test Artist')
        if success:
            print("✅ Database operations - OK")
        else:
            print("❌ Database operations - FAILED")
            return False
    except Exception as e:
        print(f"❌ Database test - FAILED: {e}")
        return False
    
    try:
        # Test music recommender
        from music_recommender import MusicRecommender
        recommender = MusicRecommender()
        recommendations = recommender.get_recommendations('happy', 'English', '', 1)
        if recommendations:
            print("✅ Music recommendations - OK")
        else:
            print("⚠️ Music recommendations - No results (expected without API keys)")
    except Exception as e:
        print(f"❌ Music recommender test - FAILED: {e}")
        return False
    
    try:
        # Test emotion detector (fallback)
        from emotion_detector import FallbackEmotionDetector
        detector = FallbackEmotionDetector()
        print("✅ Emotion detector initialization - OK")
    except Exception as e:
        print(f"❌ Emotion detector test - FAILED: {e}")
        return False
    
    return True

def run_pytest():
    """Run pytest if available"""
    try:
        result = subprocess.run(['pytest', 'tests/', '-v'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ All tests passed!")
            return True
        else:
            print("❌ Some tests failed:")
            print(result.stdout)
            print(result.stderr)
            return False
    except FileNotFoundError:
        print("⚠️ pytest not found, skipping unit tests")
        return True

def main():
    """Main test runner"""
    print("🎵 Enhanced Emotion-Based Music Recommender - Test Suite")
    print("=" * 60)
    
    print("\n📦 Checking Dependencies...")
    missing = check_dependencies()
    
    if missing:
        print(f"\n❌ Missing dependencies: {', '.join(missing)}")
        print("Please install them using: pip install -r requirements.txt")
        return False
    
    print("\n📥 Testing Module Imports...")
    if not test_imports():
        print("\n❌ Module import tests failed!")
        return False
    
    print("\n🔧 Testing Basic Functionality...")
    if not test_basic_functionality():
        print("\n❌ Basic functionality tests failed!")
        return False
    
    print("\n🧪 Running Unit Tests...")
    if not run_pytest():
        print("\n⚠️ Some unit tests failed, but core functionality works")
    
    print("\n" + "=" * 60)
    print("🎉 Test suite completed!")
    print("\n🚀 To run the application:")
    print("   streamlit run music.py")
    print("\n📖 For setup instructions, see setup_instructions.md")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
