"""
User Database and Personalization System
"""
import sqlite3
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from config import DATABASE_PATH

logger = logging.getLogger(__name__)

class UserDatabase:
    """Database manager for user preferences and feedback"""
    
    def __init__(self, db_path: str = DATABASE_PATH):
        self.db_path = db_path
        self._initialize_database()
    
    def _initialize_database(self):
        """Initialize the database with required tables"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # User sessions table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS user_sessions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT UNIQUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        language TEXT,
                        favorite_artist TEXT,
                        total_interactions INTEGER DEFAULT 0
                    )
                ''')
                
                # Emotion detections table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS emotion_detections (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT,
                        emotion TEXT,
                        confidence REAL,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (session_id) REFERENCES user_sessions (session_id)
                    )
                ''')
                
                # Music recommendations table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS music_recommendations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT,
                        emotion TEXT,
                        track_name TEXT,
                        artist TEXT,
                        source TEXT,
                        recommended_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (session_id) REFERENCES user_sessions (session_id)
                    )
                ''')
                
                # User feedback table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS user_feedback (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT,
                        recommendation_id INTEGER,
                        emotion TEXT,
                        track_name TEXT,
                        artist TEXT,
                        rating INTEGER CHECK (rating >= 1 AND rating <= 5),
                        feedback_text TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (session_id) REFERENCES user_sessions (session_id),
                        FOREIGN KEY (recommendation_id) REFERENCES music_recommendations (id)
                    )
                ''')
                
                # User preferences table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS user_preferences (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT,
                        emotion TEXT,
                        preferred_genres TEXT,  -- JSON array
                        preferred_artists TEXT, -- JSON array
                        disliked_genres TEXT,   -- JSON array
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (session_id) REFERENCES user_sessions (session_id)
                    )
                ''')
                
                conn.commit()
                logger.info("Database initialized successfully")
                
        except Exception as e:
            logger.error(f"Failed to initialize database: {str(e)}")
    
    def create_session(self, session_id: str, language: str = "", 
                      favorite_artist: str = "") -> bool:
        """Create a new user session"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO user_sessions 
                    (session_id, language, favorite_artist, total_interactions)
                    VALUES (?, ?, ?, 0)
                ''', (session_id, language, favorite_artist))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Failed to create session: {str(e)}")
            return False
    
    def log_emotion_detection(self, session_id: str, emotion: str, 
                            confidence: float) -> bool:
        """Log an emotion detection event"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO emotion_detections 
                    (session_id, emotion, confidence)
                    VALUES (?, ?, ?)
                ''', (session_id, emotion, confidence))
                
                # Update session interaction count
                cursor.execute('''
                    UPDATE user_sessions 
                    SET total_interactions = total_interactions + 1
                    WHERE session_id = ?
                ''', (session_id,))
                
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"Failed to log emotion detection: {str(e)}")
            return False
    
    def log_music_recommendation(self, session_id: str, emotion: str, 
                               track_name: str, artist: str, source: str) -> Optional[int]:
        """Log a music recommendation and return the recommendation ID"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO music_recommendations 
                    (session_id, emotion, track_name, artist, source)
                    VALUES (?, ?, ?, ?, ?)
                ''', (session_id, emotion, track_name, artist, source))
                
                recommendation_id = cursor.lastrowid
                conn.commit()
                return recommendation_id
        except Exception as e:
            logger.error(f"Failed to log music recommendation: {str(e)}")
            return None
    
    def add_user_feedback(self, session_id: str, recommendation_id: int, 
                         emotion: str, track_name: str, artist: str,
                         rating: int, feedback_text: str = "") -> bool:
        """Add user feedback for a recommendation"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO user_feedback 
                    (session_id, recommendation_id, emotion, track_name, 
                     artist, rating, feedback_text)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (session_id, recommendation_id, emotion, track_name, 
                      artist, rating, feedback_text))
                conn.commit()
                
                # Update user preferences based on feedback
                self._update_preferences_from_feedback(session_id, emotion, 
                                                     artist, rating)
                return True
        except Exception as e:
            logger.error(f"Failed to add user feedback: {str(e)}")
            return False
    
    def _update_preferences_from_feedback(self, session_id: str, emotion: str, 
                                        artist: str, rating: int):
        """Update user preferences based on feedback"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get current preferences
                cursor.execute('''
                    SELECT preferred_artists, disliked_genres 
                    FROM user_preferences 
                    WHERE session_id = ? AND emotion = ?
                ''', (session_id, emotion))
                
                result = cursor.fetchone()
                
                if result:
                    preferred_artists = json.loads(result[0] or '[]')
                    disliked_genres = json.loads(result[1] or '[]')
                else:
                    preferred_artists = []
                    disliked_genres = []
                
                # Update preferences based on rating
                if rating >= 4:  # Good rating
                    if artist not in preferred_artists:
                        preferred_artists.append(artist)
                elif rating <= 2:  # Bad rating
                    if artist in preferred_artists:
                        preferred_artists.remove(artist)
                
                # Save updated preferences
                cursor.execute('''
                    INSERT OR REPLACE INTO user_preferences 
                    (session_id, emotion, preferred_artists, disliked_genres)
                    VALUES (?, ?, ?, ?)
                ''', (session_id, emotion, json.dumps(preferred_artists), 
                      json.dumps(disliked_genres)))
                
                conn.commit()
                
        except Exception as e:
            logger.error(f"Failed to update preferences: {str(e)}")
    
    def get_user_preferences(self, session_id: str, emotion: str) -> Dict:
        """Get user preferences for a specific emotion"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT preferred_genres, preferred_artists, disliked_genres
                    FROM user_preferences 
                    WHERE session_id = ? AND emotion = ?
                ''', (session_id, emotion))
                
                result = cursor.fetchone()
                
                if result:
                    return {
                        'preferred_genres': json.loads(result[0] or '[]'),
                        'preferred_artists': json.loads(result[1] or '[]'),
                        'disliked_genres': json.loads(result[2] or '[]')
                    }
                else:
                    return {
                        'preferred_genres': [],
                        'preferred_artists': [],
                        'disliked_genres': []
                    }
                    
        except Exception as e:
            logger.error(f"Failed to get user preferences: {str(e)}")
            return {'preferred_genres': [], 'preferred_artists': [], 'disliked_genres': []}
    
    def get_emotion_statistics(self, session_id: str) -> Dict:
        """Get emotion detection statistics for a session"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get emotion counts
                cursor.execute('''
                    SELECT emotion, COUNT(*) as count, AVG(confidence) as avg_confidence
                    FROM emotion_detections 
                    WHERE session_id = ?
                    GROUP BY emotion
                    ORDER BY count DESC
                ''', (session_id,))
                
                emotion_stats = []
                for row in cursor.fetchall():
                    emotion_stats.append({
                        'emotion': row[0],
                        'count': row[1],
                        'avg_confidence': round(row[2], 2)
                    })
                
                # Get total detections
                cursor.execute('''
                    SELECT COUNT(*) FROM emotion_detections WHERE session_id = ?
                ''', (session_id,))
                total_detections = cursor.fetchone()[0]
                
                return {
                    'total_detections': total_detections,
                    'emotion_breakdown': emotion_stats,
                    'most_common_emotion': emotion_stats[0]['emotion'] if emotion_stats else 'neutral'
                }
                
        except Exception as e:
            logger.error(f"Failed to get emotion statistics: {str(e)}")
            return {'total_detections': 0, 'emotion_breakdown': [], 'most_common_emotion': 'neutral'}
    
    def get_feedback_statistics(self, session_id: str) -> Dict:
        """Get feedback statistics for a session"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Get average rating by emotion
                cursor.execute('''
                    SELECT emotion, AVG(rating) as avg_rating, COUNT(*) as feedback_count
                    FROM user_feedback 
                    WHERE session_id = ?
                    GROUP BY emotion
                ''', (session_id,))
                
                feedback_stats = []
                for row in cursor.fetchall():
                    feedback_stats.append({
                        'emotion': row[0],
                        'avg_rating': round(row[1], 2),
                        'feedback_count': row[2]
                    })
                
                # Get overall satisfaction
                cursor.execute('''
                    SELECT AVG(rating) FROM user_feedback WHERE session_id = ?
                ''', (session_id,))
                overall_rating = cursor.fetchone()[0] or 0
                
                return {
                    'overall_satisfaction': round(overall_rating, 2),
                    'emotion_feedback': feedback_stats
                }
                
        except Exception as e:
            logger.error(f"Failed to get feedback statistics: {str(e)}")
            return {'overall_satisfaction': 0, 'emotion_feedback': []}
