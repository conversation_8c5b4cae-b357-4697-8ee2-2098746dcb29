"""
Demo version of Enhanced Emotion-Based Music Recommender
Simplified for demonstration without heavy dependencies
"""
import streamlit as st
import random
import time
from datetime import datetime

# Configure page
st.set_page_config(
    page_title="Enhanced Emotion Music Recommender - Demo",
    page_icon="🎵",
    layout="wide"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        text-align: center;
        color: #1DB954;
        font-size: 3rem;
        font-weight: bold;
        margin-bottom: 1rem;
    }
    .emotion-display {
        background: linear-gradient(90deg, #1DB954, #1ed760);
        color: white;
        padding: 1rem;
        border-radius: 10px;
        text-align: center;
        font-size: 1.5rem;
        margin: 1rem 0;
    }
    .recommendation-card {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 10px;
        padding: 1rem;
        margin: 0.5rem 0;
    }
</style>
""", unsafe_allow_html=True)

# App header
st.markdown('<h1 class="main-header">🎵 Enhanced Emotion-Based Music Recommender</h1>', unsafe_allow_html=True)
st.markdown('<p style="text-align: center; font-size: 1.2rem; color: #666;">AI-powered music recommendations based on real-time emotion detection</p>', unsafe_allow_html=True)

# Emotion mappings
EMOTIONS = ['happy', 'sad', 'angry', 'fear', 'surprise', 'disgust', 'neutral']
EMOTION_EMOJIS = {
    'happy': '😊',
    'sad': '😢',
    'angry': '😠',
    'fear': '😨',
    'surprise': '😲',
    'disgust': '🤢',
    'neutral': '😐'
}

# Sample music recommendations
SAMPLE_RECOMMENDATIONS = {
    'happy': [
        {'name': 'Happy', 'artist': 'Pharrell Williams', 'album': 'G I R L'},
        {'name': 'Can\'t Stop the Feeling!', 'artist': 'Justin Timberlake', 'album': 'Trolls'},
        {'name': 'Uptown Funk', 'artist': 'Mark Ronson ft. Bruno Mars', 'album': 'Uptown Special'},
    ],
    'sad': [
        {'name': 'Someone Like You', 'artist': 'Adele', 'album': '21'},
        {'name': 'Hurt', 'artist': 'Johnny Cash', 'album': 'American IV'},
        {'name': 'Mad World', 'artist': 'Gary Jules', 'album': 'Donnie Darko Soundtrack'},
    ],
    'angry': [
        {'name': 'Break Stuff', 'artist': 'Limp Bizkit', 'album': 'Significant Other'},
        {'name': 'Killing in the Name', 'artist': 'Rage Against the Machine', 'album': 'Rage Against the Machine'},
    ],
    'neutral': [
        {'name': 'Weightless', 'artist': 'Marconi Union', 'album': 'Weightless'},
        {'name': 'Clair de Lune', 'artist': 'Claude Debussy', 'album': 'Suite Bergamasque'},
    ]
}

# Initialize session state
if 'current_emotion' not in st.session_state:
    st.session_state.current_emotion = 'neutral'
    st.session_state.confidence = 0.0
    st.session_state.recommendations = []
    st.session_state.detection_count = 0

# Sidebar
with st.sidebar:
    st.header("🎵 User Preferences")
    language = st.text_input("Preferred Language", value="English")
    favorite_artist = st.text_input("Favorite Artist", placeholder="e.g., Taylor Swift")
    
    st.divider()
    
    st.header("😊 Current Emotion")
    if st.session_state.current_emotion:
        emoji = EMOTION_EMOJIS.get(st.session_state.current_emotion, '😐')
        confidence_color = "green" if st.session_state.confidence > 0.7 else "orange" if st.session_state.confidence > 0.4 else "red"
        
        st.markdown(f"""
        <div class="emotion-display">
            <div style="font-size: 3rem;">{emoji}</div>
            <div style="font-size: 1.5rem; font-weight: bold;">{st.session_state.current_emotion.title()}</div>
            <div style="color: {confidence_color};">Confidence: {st.session_state.confidence:.2f}</div>
        </div>
        """, unsafe_allow_html=True)
    
    st.divider()
    
    st.header("📊 Session Stats")
    st.metric("Detections", st.session_state.detection_count)
    st.metric("Recommendations", len(st.session_state.recommendations))

# Main content
col1, col2 = st.columns([2, 1])

with col1:
    st.header("📹 Emotion Detection Demo")
    
    st.info("🎭 This is a demo version. In the full version, this would show live webcam feed with real-time emotion detection using DeepFace AI.")
    
    # Simulate emotion detection
    if st.button("🎭 Simulate Emotion Detection", type="primary"):
        with st.spinner("🔍 Analyzing your emotion..."):
            time.sleep(2)  # Simulate processing time
            
            # Randomly select emotion for demo
            detected_emotion = random.choice(EMOTIONS)
            confidence = random.uniform(0.6, 0.95)
            
            st.session_state.current_emotion = detected_emotion
            st.session_state.confidence = confidence
            st.session_state.detection_count += 1
            
            st.success(f"✅ Detected emotion: {detected_emotion.title()} (Confidence: {confidence:.2f})")
            st.rerun()

with col2:
    st.header("🎵 Music Recommendations")
    
    if st.button("🎧 Get Music Recommendations", type="primary", use_container_width=True):
        if st.session_state.current_emotion and st.session_state.current_emotion != "neutral":
            with st.spinner("🎵 Finding perfect songs for your mood..."):
                time.sleep(1)
                
                # Get sample recommendations
                emotion_recs = SAMPLE_RECOMMENDATIONS.get(st.session_state.current_emotion, [])
                if not emotion_recs:
                    emotion_recs = SAMPLE_RECOMMENDATIONS['neutral']
                
                st.session_state.recommendations = emotion_recs
                st.success(f"🎵 Found {len(emotion_recs)} songs for your {st.session_state.current_emotion} mood!")
        else:
            st.warning("⚠️ Please detect your emotion first!")
    
    # Display recommendations
    if st.session_state.recommendations:
        st.subheader(f"🎵 Songs for your {st.session_state.current_emotion} mood:")
        
        for i, rec in enumerate(st.session_state.recommendations):
            st.markdown(f"""
            <div class="recommendation-card">
                <h4>🎵 {rec['name']}</h4>
                <p><strong>Artist:</strong> {rec['artist']}</p>
                <p><strong>Album:</strong> {rec['album']}</p>
            </div>
            """, unsafe_allow_html=True)
            
            col_play, col_feedback = st.columns(2)
            with col_play:
                if st.button(f"▶️ Play", key=f"play_{i}"):
                    st.info("🎵 In full version: Opens Spotify/YouTube")
            
            with col_feedback:
                if st.button(f"⭐ Rate", key=f"rate_{i}"):
                    rating = st.slider(f"Rate {rec['name']}", 1, 5, 3, key=f"rating_{i}")
                    if st.button(f"Submit Rating", key=f"submit_{i}"):
                        st.success("✅ Thank you for your feedback!")

# Analytics Dashboard
st.header("📊 Analytics Dashboard")

tab1, tab2, tab3 = st.tabs(["📈 Emotion Statistics", "🎵 Music Preferences", "📝 Feedback Summary"])

with tab1:
    st.subheader("📈 Your Emotion Patterns")
    
    if st.session_state.detection_count > 0:
        # Simulate emotion distribution
        emotion_data = {
            'Emotion': ['Happy', 'Sad', 'Neutral', 'Surprise'],
            'Count': [5, 2, 3, 1],
            'Confidence': [0.85, 0.72, 0.68, 0.91]
        }
        
        st.bar_chart(emotion_data, x='Emotion', y='Count')
        st.line_chart(emotion_data, x='Emotion', y='Confidence')
    else:
        st.info("📊 Start using emotion detection to see your analytics!")

with tab2:
    st.subheader("🎵 Your Music Journey")
    st.info("🎵 Music recommendation history and preferences will appear here as you use the app!")

with tab3:
    st.subheader("📝 Your Feedback Summary")
    st.info("📝 Give feedback on recommendations to see your satisfaction trends!")

# Footer
st.divider()
st.markdown("""
<div style="text-align: center; color: #666; padding: 2rem;">
    <p>🎵 Enhanced Emotion-Based Music Recommender - Demo Version</p>
    <p>Full version powered by DeepFace AI, Spotify API, and Advanced ML</p>
    <p>Made with ❤️ for better music discovery</p>
</div>
""", unsafe_allow_html=True)

# Technical details expander
with st.expander("🔧 Technical Architecture (Click to expand)"):
    st.markdown("""
    ### 🏗️ Full System Architecture:
    
    **Frontend:**
    - Streamlit with custom CSS for responsive UI
    - Real-time webcam integration with WebRTC
    - Interactive charts with Plotly/Altair
    
    **AI/ML Components:**
    - DeepFace for facial emotion recognition
    - TensorFlow/Keras for model inference
    - OpenCV for computer vision processing
    - MediaPipe for facial landmark detection
    
    **Backend Services:**
    - Spotify Web API for music recommendations
    - SQLite database for user data persistence
    - RESTful API design patterns
    - Session management and user preferences
    
    **Data Processing:**
    - Real-time emotion detection pipeline
    - Confidence scoring and smoothing algorithms
    - Personalized recommendation engine
    - Feedback learning system
    
    **Deployment:**
    - Docker containerization
    - Cloud deployment (Streamlit Cloud, Heroku, Render)
    - Environment configuration management
    - Automated testing and CI/CD
    """)
