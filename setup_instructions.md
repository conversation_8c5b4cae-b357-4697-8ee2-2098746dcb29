# Setup Instructions for Enhanced Emotion-Based Music Recommender

## Quick Setup Guide

### 1. Install Dependencies

```bash
# Install Python dependencies
pip install -r requirements.txt
```

### 2. Set up Spotify API (Optional but Recommended)

1. Go to [Spotify Developer Dashboard](https://developer.spotify.com/dashboard/)
2. Create a new app
3. Copy your Client ID and Client Secret
4. Create a `.env` file in the project root:

```env
SPOTIFY_CLIENT_ID=your_client_id_here
SPOTIFY_CLIENT_SECRET=your_client_secret_here
```

### 3. Run the Application

```bash
streamlit run music.py
```

### 4. Access the App

Open your browser and go to `http://localhost:8501`

## Troubleshooting

### Common Issues

1. **Webcam not working**: Make sure your browser has camera permissions
2. **DeepFace installation issues**: Try installing with `pip install deepface --no-deps` then install dependencies manually
3. **TensorFlow issues**: Make sure you have compatible Python version (3.8-3.11)

### Performance Tips

1. **For better performance**: Use a dedicated GPU if available
2. **For deployment**: Use `opencv-python-headless` instead of `opencv-python`
3. **Memory optimization**: Reduce detection interval in `EnhancedEmotionProcessor`

## Testing

Run the test suite:

```bash
# Install test dependencies
pip install pytest pytest-cov

# Run all tests
pytest tests/ -v

# Run with coverage
pytest tests/ -v --cov=.
```

## Deployment Options

### Streamlit Cloud
1. Push to GitHub
2. Connect to Streamlit Cloud
3. Add environment variables
4. Deploy

### Heroku
1. Create Heroku app
2. Add environment variables
3. Deploy using Git or GitHub integration

### Docker
```bash
docker build -t emotion-music-app .
docker run -p 8501:8501 emotion-music-app
```
