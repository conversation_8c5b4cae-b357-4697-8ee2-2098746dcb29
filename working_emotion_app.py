"""
Working Emotion-Based Music Recommender
Real emotion detection using facial features analysis
"""
import streamlit as st
from streamlit_webrtc import webrtc_streamer, VideoProcessorBase
import av
import cv2 
import numpy as np 
import webbrowser
import math

# Configure page
st.set_page_config(
    page_title="Emotion Based Music Recommender",
    page_icon="🎶",
    layout="wide"
)

st.header("Emotion Based Music Recommender 🎶")

# Initialize session state
if "run" not in st.session_state:
    st.session_state["run"] = "true"
    st.session_state["detected_emotion"] = ""

try:
    emotion = np.load("emotion.npy")[0]
except:
    emotion = ""

if not(emotion):
    st.session_state["run"] = "true"
else:
    st.session_state["run"] = "false"

class RealEmotionProcessor(VideoProcessorBase):
    def __init__(self):
        # Load face and eye cascades
        self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        self.eye_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_eye.xml')
        self.smile_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_smile.xml')
        
        # Your original emotion labels
        self.emotions = ['happy', 'neutral', 'surprise', 'angry', 'sad']
        self.current_emotion = 'neutral'
        self.frame_count = 0
        self.emotion_history = []
        
    def detect_emotion_from_features(self, face_roi, gray_face):
        """Detect emotion based on facial features"""
        
        # Detect eyes and smile in the face region
        eyes = self.eye_cascade.detectMultiScale(gray_face, 1.1, 5)
        smiles = self.smile_cascade.detectMultiScale(gray_face, 1.8, 20)
        
        # Calculate face dimensions
        face_height, face_width = gray_face.shape
        
        # Feature analysis
        num_eyes = len(eyes)
        num_smiles = len(smiles)
        
        # Analyze eye characteristics
        eye_openness = 0
        if num_eyes >= 2:
            # Calculate average eye height (openness indicator)
            total_eye_height = sum([h for (x, y, w, h) in eyes])
            eye_openness = total_eye_height / len(eyes) / face_height
        
        # Analyze mouth/smile characteristics
        smile_intensity = 0
        if num_smiles > 0:
            # Calculate smile size relative to face
            total_smile_area = sum([w * h for (x, y, w, h) in smiles])
            smile_intensity = total_smile_area / (face_width * face_height)
        
        # Simple emotion detection logic based on features
        if smile_intensity > 0.02:  # Strong smile detected
            emotion = 'happy'
        elif eye_openness > 0.08:  # Wide eyes
            emotion = 'surprise'
        elif eye_openness < 0.03 and num_eyes < 2:  # Squinted or closed eyes
            emotion = 'angry'
        elif smile_intensity < 0.005 and eye_openness < 0.05:  # No smile, droopy eyes
            emotion = 'sad'
        else:
            emotion = 'neutral'
        
        return emotion
    
    def recv(self, frame):
        frm = frame.to_ndarray(format="bgr24")
        frm = cv2.flip(frm, 1)
        
        # Convert to grayscale for face detection
        gray = cv2.cvtColor(frm, cv2.COLOR_BGR2GRAY)
        faces = self.face_cascade.detectMultiScale(gray, 1.3, 5)
        
        # Process each detected face
        for (x, y, w, h) in faces:
            # Draw rectangle around face
            cv2.rectangle(frm, (x, y), (x+w, y+h), (255, 0, 0), 2)
            
            # Extract face region for emotion analysis
            face_roi = frm[y:y+h, x:x+w]
            gray_face = gray[y:y+h, x:x+w]
            
            # Detect emotion every 10 frames for stability
            if self.frame_count % 10 == 0:
                detected_emotion = self.detect_emotion_from_features(face_roi, gray_face)
                
                # Add to history for smoothing
                self.emotion_history.append(detected_emotion)
                if len(self.emotion_history) > 5:
                    self.emotion_history.pop(0)
                
                # Use most common emotion from recent history
                if self.emotion_history:
                    emotion_counts = {}
                    for e in self.emotion_history:
                        emotion_counts[e] = emotion_counts.get(e, 0) + 1
                    self.current_emotion = max(emotion_counts, key=emotion_counts.get)
                
                # Save emotion to file (like your original code)
                np.save("emotion.npy", np.array([self.current_emotion]))
                st.session_state["detected_emotion"] = self.current_emotion
            
            # Detect and draw facial features for visual feedback
            face_roi_gray = gray[y:y+h, x:x+w]
            
            # Draw eyes
            eyes = self.eye_cascade.detectMultiScale(face_roi_gray, 1.1, 5)
            for (ex, ey, ew, eh) in eyes:
                cv2.rectangle(frm, (x+ex, y+ey), (x+ex+ew, y+ey+eh), (0, 255, 0), 2)
            
            # Draw smile
            smiles = self.smile_cascade.detectMultiScale(face_roi_gray, 1.8, 20)
            for (sx, sy, sw, sh) in smiles:
                cv2.rectangle(frm, (x+sx, y+sy), (x+sx+sw, y+sy+sh), (0, 0, 255), 2)
        
        # Display current emotion on frame
        emotion_text = f"Emotion: {self.current_emotion.upper()}"
        cv2.putText(frm, emotion_text, (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 0), 2)
        
        # Add instructions
        if len(faces) == 0:
            cv2.putText(frm, "Show your face to the camera", (50, 100), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        else:
            cv2.putText(frm, "Try different expressions!", (50, 100), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        self.frame_count += 1
        return av.VideoFrame.from_ndarray(frm, format="bgr24")

# User input section
col1, col2 = st.columns(2)

with col1:
    lang = st.text_input("Preferred Language", value="English")
    singer = st.text_input("Favorite Singer", placeholder="e.g., Taylor Swift")

with col2:
    st.write("**Current Detected Emotion:**")
    if st.session_state.get("detected_emotion"):
        emotion_emoji = {
            'happy': '😊', 'sad': '😢', 'angry': '😠', 
            'surprise': '😲', 'neutral': '😐'
        }
        emoji = emotion_emoji.get(st.session_state["detected_emotion"], '😐')
        st.success(f"{emoji} {st.session_state['detected_emotion'].title()}")
    else:
        st.info("🎭 No emotion detected yet")

# Webcam section
st.subheader("📹 Live Emotion Detection")

if lang and singer:
    st.info("📸 **How to get accurate emotion detection:**")
    st.write("""
    - **😊 Happy**: Smile naturally at the camera
    - **😢 Sad**: Look down with a neutral or frowning expression  
    - **😠 Angry**: Squint your eyes or furrow your brow
    - **😲 Surprise**: Open your eyes wide
    - **😐 Neutral**: Keep a relaxed, normal expression
    """)
    
    webrtc_streamer(
        key="real_emotion_detection",
        video_processor_factory=RealEmotionProcessor,
        media_stream_constraints={
            "video": {"width": 640, "height": 480},
            "audio": False
        }
    )
else:
    st.warning("⚠️ Please enter your preferred language and favorite singer to start emotion detection.")

# Music recommendation section
st.subheader("🎧 Music Recommendations")

btn = st.button("🎧 Recommend Me Songs", type="primary")

if btn:
    try:
        emotion = np.load("emotion.npy")[0]
    except:
        emotion = ""
    
    if not(emotion):
        st.warning("⚠️ Please let me capture your emotion first using the webcam above.")
        st.session_state["run"] = "true"
    else:
        st.success(f"🎵 Found music for your **{emotion}** mood!")
        
        # Create YouTube search URL (like your original)
        search_query = f"{lang}+{emotion}+song+{singer}".replace(" ", "+")
        youtube_url = f"https://www.youtube.com/results?search_query={search_query}"
        
        # Display the link
        st.markdown(f"### [🎵 Click here to open YouTube search]({youtube_url})")
        
        # Show emotion-specific recommendations
        st.write("**Recommended for your mood:**")
        
        emotion_songs = {
            'happy': ['Happy - Pharrell Williams', 'Good as Hell - Lizzo', 'Can\'t Stop the Feeling - Justin Timberlake'],
            'sad': ['Someone Like You - Adele', 'Hurt - Johnny Cash', 'Mad World - Gary Jules'],
            'angry': ['Break Stuff - Limp Bizkit', 'Killing in the Name - Rage Against the Machine', 'Bodies - Drowning Pool'],
            'neutral': ['Weightless - Marconi Union', 'Clair de Lune - Debussy', 'River - Joni Mitchell'],
            'surprise': ['Bohemian Rhapsody - Queen', 'Mr. Blue Sky - ELO', 'September - Earth Wind & Fire']
        }
        
        songs = emotion_songs.get(emotion, emotion_songs['neutral'])
        for song in songs:
            st.write(f"🎵 {song}")
        
        # Clear emotion for next detection (like your original)
        np.save("emotion.npy", np.array([""]))
        st.session_state["run"] = "false"
        st.session_state["detected_emotion"] = ""

# Footer
st.divider()
st.markdown("""
<div style="text-align: center; color: #666;">
    <p>🎶 Emotion-Based Music Recommender</p>
    <p>Real-time facial emotion detection with music recommendations</p>
</div>
""", unsafe_allow_html=True)
