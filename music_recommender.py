"""
Music Recommendation Module with Spotify API Integration
"""
import spotipy
from spotipy.oauth2 import SpotifyClientCredentials
import requests
import json
import logging
from typing import List, Dict, Optional
from config import (
    SPOTIFY_CLIENT_ID, 
    SPOTIFY_CLIENT_SECRET, 
    EMOTION_MUSIC_MAPPING,
    YOUTUBE_API_KEY
)

logger = logging.getLogger(__name__)

class MusicRecommender:
    """Enhanced music recommender with Spotify API integration"""
    
    def __init__(self):
        self.spotify = None
        self.fallback_playlists = self._load_fallback_playlists()
        self._initialize_spotify()
    
    def _initialize_spotify(self):
        """Initialize Spotify API client"""
        try:
            if SPOTIFY_CLIENT_ID and SPOTIFY_CLIENT_SECRET:
                client_credentials_manager = SpotifyClientCredentials(
                    client_id=SPOTIFY_CLIENT_ID,
                    client_secret=SPOTIFY_CLIENT_SECRET
                )
                self.spotify = spotipy.Spotify(
                    client_credentials_manager=client_credentials_manager
                )
                logger.info("Spotify API initialized successfully")
            else:
                logger.warning("Spotify credentials not found, using fallback mode")
        except Exception as e:
            logger.error(f"Failed to initialize Spotify API: {str(e)}")
            self.spotify = None
    
    def get_recommendations(self, emotion: str, language: str = "english", 
                          artist: str = "", limit: int = 10) -> List[Dict]:
        """
        Get music recommendations based on emotion
        
        Args:
            emotion: Detected emotion
            language: Preferred language
            artist: Preferred artist
            limit: Number of recommendations
            
        Returns:
            List of recommended tracks
        """
        try:
            if self.spotify:
                return self._get_spotify_recommendations(emotion, language, artist, limit)
            else:
                return self._get_fallback_recommendations(emotion, language, artist, limit)
        except Exception as e:
            logger.error(f"Failed to get recommendations: {str(e)}")
            return self._get_fallback_recommendations(emotion, language, artist, limit)
    
    def _get_spotify_recommendations(self, emotion: str, language: str, 
                                   artist: str, limit: int) -> List[Dict]:
        """Get recommendations from Spotify API"""
        try:
            # Get emotion-based search terms
            search_terms = EMOTION_MUSIC_MAPPING.get(emotion, ['chill'])
            
            # Build search query
            query_parts = []
            if artist:
                query_parts.append(f'artist:"{artist}"')
            
            # Add emotion-based genres/moods
            for term in search_terms[:2]:  # Use first 2 terms
                query_parts.append(f'genre:"{term}"')
            
            if language and language.lower() != 'english':
                query_parts.append(f'"{language}"')
            
            query = ' OR '.join(query_parts) if query_parts else search_terms[0]
            
            # Search for tracks
            results = self.spotify.search(
                q=query,
                type='track',
                limit=limit,
                market='US'
            )
            
            recommendations = []
            for track in results['tracks']['items']:
                recommendations.append({
                    'name': track['name'],
                    'artist': ', '.join([artist['name'] for artist in track['artists']]),
                    'album': track['album']['name'],
                    'duration': track['duration_ms'],
                    'preview_url': track['preview_url'],
                    'external_url': track['external_urls']['spotify'],
                    'image_url': track['album']['images'][0]['url'] if track['album']['images'] else None,
                    'popularity': track['popularity'],
                    'source': 'spotify'
                })
            
            logger.info(f"Found {len(recommendations)} Spotify recommendations for {emotion}")
            return recommendations
            
        except Exception as e:
            logger.error(f"Spotify API error: {str(e)}")
            return []
    
    def _get_fallback_recommendations(self, emotion: str, language: str, 
                                    artist: str, limit: int) -> List[Dict]:
        """Get fallback recommendations from predefined playlists"""
        try:
            emotion_playlists = self.fallback_playlists.get(emotion, [])
            
            # Filter by artist if specified
            if artist:
                emotion_playlists = [
                    track for track in emotion_playlists 
                    if artist.lower() in track['artist'].lower()
                ]
            
            # Return limited results
            recommendations = emotion_playlists[:limit]
            
            # Add YouTube search URLs
            for track in recommendations:
                search_query = f"{track['artist']} {track['name']} {language}"
                track['youtube_url'] = f"https://www.youtube.com/results?search_query={search_query.replace(' ', '+')}"
                track['source'] = 'fallback'
            
            logger.info(f"Found {len(recommendations)} fallback recommendations for {emotion}")
            return recommendations
            
        except Exception as e:
            logger.error(f"Fallback recommendations error: {str(e)}")
            return []
    
    def _load_fallback_playlists(self) -> Dict[str, List[Dict]]:
        """Load predefined fallback playlists"""
        return {
            'happy': [
                {'name': 'Happy', 'artist': 'Pharrell Williams', 'album': 'G I R L'},
                {'name': 'Can\'t Stop the Feeling!', 'artist': 'Justin Timberlake', 'album': 'Trolls'},
                {'name': 'Uptown Funk', 'artist': 'Mark Ronson ft. Bruno Mars', 'album': 'Uptown Special'},
                {'name': 'Good as Hell', 'artist': 'Lizzo', 'album': 'Cuz I Love You'},
                {'name': 'Walking on Sunshine', 'artist': 'Katrina and the Waves', 'album': 'Walking on Sunshine'},
            ],
            'sad': [
                {'name': 'Someone Like You', 'artist': 'Adele', 'album': '21'},
                {'name': 'Hurt', 'artist': 'Johnny Cash', 'album': 'American IV'},
                {'name': 'Mad World', 'artist': 'Gary Jules', 'album': 'Donnie Darko Soundtrack'},
                {'name': 'The Sound of Silence', 'artist': 'Simon & Garfunkel', 'album': 'Sounds of Silence'},
                {'name': 'Tears in Heaven', 'artist': 'Eric Clapton', 'album': 'Unplugged'},
            ],
            'angry': [
                {'name': 'Break Stuff', 'artist': 'Limp Bizkit', 'album': 'Significant Other'},
                {'name': 'Killing in the Name', 'artist': 'Rage Against the Machine', 'album': 'Rage Against the Machine'},
                {'name': 'Bodies', 'artist': 'Drowning Pool', 'album': 'Sinner'},
                {'name': 'Chop Suey!', 'artist': 'System of a Down', 'album': 'Toxicity'},
                {'name': 'Du Hast', 'artist': 'Rammstein', 'album': 'Sehnsucht'},
            ],
            'fear': [
                {'name': 'Weightless', 'artist': 'Marconi Union', 'album': 'Weightless'},
                {'name': 'Clair de Lune', 'artist': 'Claude Debussy', 'album': 'Suite Bergamasque'},
                {'name': 'Aqueous Transmission', 'artist': 'Incubus', 'album': 'Morning View'},
                {'name': 'Spiegel im Spiegel', 'artist': 'Arvo Pärt', 'album': 'Alina'},
                {'name': 'On Earth as It Is in Heaven', 'artist': 'Angels & Airwaves', 'album': 'Love'},
            ],
            'surprise': [
                {'name': 'Bohemian Rhapsody', 'artist': 'Queen', 'album': 'A Night at the Opera'},
                {'name': 'Mr. Blue Sky', 'artist': 'Electric Light Orchestra', 'album': 'Out of the Blue'},
                {'name': 'September', 'artist': 'Earth, Wind & Fire', 'album': 'The Best of Earth, Wind & Fire'},
                {'name': 'I Want It That Way', 'artist': 'Backstreet Boys', 'album': 'Millennium'},
                {'name': 'Don\'t Stop Me Now', 'artist': 'Queen', 'album': 'Jazz'},
            ],
            'disgust': [
                {'name': 'Creep', 'artist': 'Radiohead', 'album': 'Pablo Honey'},
                {'name': 'Hurt', 'artist': 'Nine Inch Nails', 'album': 'The Downward Spiral'},
                {'name': 'Black', 'artist': 'Pearl Jam', 'album': 'Ten'},
                {'name': 'Everybody Hurts', 'artist': 'R.E.M.', 'album': 'Automatic for the People'},
                {'name': 'Losing Religion', 'artist': 'R.E.M.', 'album': 'Out of Time'},
            ],
            'neutral': [
                {'name': 'Lofi Hip Hop Radio', 'artist': 'ChilledCow', 'album': 'Lofi Hip Hop'},
                {'name': 'Weightless', 'artist': 'Marconi Union', 'album': 'Weightless'},
                {'name': 'River', 'artist': 'Joni Mitchell', 'album': 'Blue'},
                {'name': 'The Night We Met', 'artist': 'Lord Huron', 'album': 'Strange Trails'},
                {'name': 'Holocene', 'artist': 'Bon Iver', 'album': 'Bon Iver, Bon Iver'},
            ]
        }
    
    def get_playlist_by_emotion(self, emotion: str) -> Optional[str]:
        """Get a Spotify playlist URL for the given emotion"""
        try:
            if not self.spotify:
                return None
            
            # Search for emotion-based playlists
            search_terms = EMOTION_MUSIC_MAPPING.get(emotion, ['chill'])
            query = f"{emotion} {search_terms[0]} playlist"
            
            results = self.spotify.search(
                q=query,
                type='playlist',
                limit=5,
                market='US'
            )
            
            if results['playlists']['items']:
                playlist = results['playlists']['items'][0]
                return playlist['external_urls']['spotify']
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get playlist: {str(e)}")
            return None
    
    def search_youtube_music(self, emotion: str, language: str, artist: str) -> str:
        """Generate YouTube search URL for music"""
        search_terms = EMOTION_MUSIC_MAPPING.get(emotion, ['music'])
        query_parts = [language, emotion, 'song']
        
        if artist:
            query_parts.append(artist)
        
        query_parts.extend(search_terms[:1])  # Add one emotion-based term
        
        search_query = '+'.join(query_parts)
        return f"https://www.youtube.com/results?search_query={search_query}"
