# 🎵 Enhanced Emotion-Based Music Recommender

An AI-powered music recommendation system that detects your emotions in real-time and suggests personalized music to match your mood. Built with advanced computer vision, machine learning, and music APIs for an immersive user experience.

## 🌟 Features

### 🔍 Advanced Emotion Detection
- **DeepFace Integration**: State-of-the-art facial emotion recognition
- **7 Emotion Categories**: Happy, Sad, Angry, Fear, Surprise, Disgust, Neutral
- **Real-time Processing**: Live emotion detection with confidence scoring
- **Fallback Support**: Backup MediaPipe-based detection for reliability

### 🎧 Smart Music Recommendations
- **Spotify API Integration**: Dynamic music recommendations from Spotify's vast library
- **Personalized Suggestions**: Tailored recommendations based on detected emotions
- **Multi-language Support**: Music recommendations in your preferred language
- **Artist Preferences**: Incorporate your favorite artists into recommendations
- **Fallback Playlists**: Curated playlists when API is unavailable

### 🎨 Enhanced User Interface
- **Modern Design**: Clean, responsive Streamlit interface with custom CSS
- **Real-time Emotion Display**: Live emotion visualization with emojis and confidence
- **Interactive Controls**: Play buttons, feedback forms, and music controls
- **Loading Indicators**: Smooth user experience with progress indicators
- **Mobile Responsive**: Works seamlessly across devices

### 💡 Personalization & Learning
- **User Feedback System**: Rate recommendations to improve future suggestions
- **Preference Learning**: System adapts based on your feedback over time
- **SQLite Database**: Secure local storage of user preferences and history
- **Session Management**: Persistent user sessions across app usage

### 📊 Analytics Dashboard
- **Emotion Statistics**: Track your emotion patterns over time
- **Music Preferences**: Visualize your music taste evolution
- **Feedback Analysis**: Monitor recommendation satisfaction trends
- **Interactive Charts**: Beautiful Plotly visualizations

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- Webcam access for emotion detection
- Spotify Developer Account (optional, for enhanced recommendations)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/emotion-music-recommender.git
   cd emotion-music-recommender
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up Spotify API (Optional)**
   - Create a Spotify Developer account at https://developer.spotify.com/
   - Create a new app and get your Client ID and Client Secret
   - Copy `.env.example` to `.env` and add your credentials:
     ```
     SPOTIFY_CLIENT_ID=your_client_id_here
     SPOTIFY_CLIENT_SECRET=your_client_secret_here
     ```

4. **Run the application**
   ```bash
   streamlit run music.py
   ```

5. **Open your browser**
   - Navigate to `http://localhost:8501`
   - Allow webcam access when prompted

## 🎯 How to Use

1. **Setup Your Preferences**
   - Enter your preferred language in the sidebar
   - Add your favorite artist (optional)

2. **Start Emotion Detection**
   - Choose "Real-time" mode for continuous detection
   - Or use "On-demand" mode for manual detection
   - Allow webcam access and position your face in the frame

3. **Get Music Recommendations**
   - Wait for emotion detection to identify your mood
   - Click "Get Music Recommendations" to find matching songs
   - Browse through personalized suggestions

4. **Provide Feedback**
   - Rate recommendations from 1-5 stars
   - Add optional text feedback
   - Help the system learn your preferences

5. **Explore Analytics**
   - View your emotion patterns in the Analytics Dashboard
   - Track your music preferences over time
   - Monitor recommendation satisfaction

## 🛠️ Technical Architecture

### Core Components
- **emotion_detector.py**: DeepFace-based emotion detection with fallback support
- **music_recommender.py**: Spotify API integration and recommendation logic
- **user_database.py**: SQLite database for user data and preferences
- **config.py**: Configuration management and constants
- **music.py**: Main Streamlit application

### Technology Stack
- **Frontend**: Streamlit with custom CSS
- **Emotion Detection**: DeepFace, OpenCV, MediaPipe
- **Music API**: Spotify Web API
- **Database**: SQLite
- **Visualization**: Plotly, Altair
- **ML Libraries**: TensorFlow, scikit-learn

## 📁 Project Structure

```
emotion-music-recommender/
├── music.py                 # Main Streamlit application
├── emotion_detector.py      # Enhanced emotion detection module
├── music_recommender.py     # Spotify API integration
├── user_database.py        # Database management
├── config.py               # Configuration settings
├── requirements.txt        # Python dependencies
├── .env.example            # Environment variables template
├── README.md              # Project documentation
├── model.h5               # Fallback emotion detection model
├── labels.npy             # Emotion labels for fallback model
└── liveEmoji/             # Original emotion detection code
    ├── data_collection.py
    ├── data_training.py
    ├── inference.py
    └── *.npy              # Training data files
```

## 🔧 Configuration

### Environment Variables
Create a `.env` file with the following variables:

```env
# Spotify API Credentials
SPOTIFY_CLIENT_ID=your_spotify_client_id
SPOTIFY_CLIENT_SECRET=your_spotify_client_secret

# Optional: YouTube API for fallback
YOUTUBE_API_KEY=your_youtube_api_key
```

### Customization Options
- **Emotion Categories**: Modify `EMOTION_LABELS` in `config.py`
- **Music Mapping**: Adjust `EMOTION_MUSIC_MAPPING` for different genres
- **UI Themes**: Customize CSS in `music.py`
- **Detection Sensitivity**: Tune confidence thresholds in `emotion_detector.py`

## 🧪 Testing

Run the test suite to ensure everything works correctly:

```bash
# Install test dependencies
pip install pytest pytest-cov

# Run tests
pytest tests/ -v --cov=.

# Run specific test categories
pytest tests/test_emotion_detection.py -v
pytest tests/test_music_recommendations.py -v
pytest tests/test_database.py -v
```

## 🚀 Deployment

### Streamlit Cloud
1. Push your code to GitHub
2. Connect your repository to Streamlit Cloud
3. Add environment variables in the Streamlit Cloud dashboard
4. Deploy with one click

### Heroku
1. Create a `Procfile`:
   ```
   web: streamlit run music.py --server.port=$PORT --server.address=0.0.0.0
   ```
2. Deploy using Heroku CLI or GitHub integration

### Docker
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8501
CMD ["streamlit", "run", "music.py"]
```

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **DeepFace**: For advanced facial emotion recognition
- **Spotify**: For providing comprehensive music API
- **Streamlit**: For the amazing web app framework
- **OpenCV & MediaPipe**: For computer vision capabilities

## 📞 Support

If you encounter any issues or have questions:

1. Check the [Issues](https://github.com/yourusername/emotion-music-recommender/issues) page
2. Create a new issue with detailed information
3. Join our [Discord community](https://discord.gg/your-invite) for real-time help

## 🎯 Future Enhancements

- [ ] Voice emotion detection
- [ ] Group emotion detection for parties
- [ ] Integration with more music platforms (Apple Music, YouTube Music)
- [ ] Mobile app development
- [ ] Social features and playlist sharing
- [ ] Advanced ML models for better accuracy

---

**Made with ❤️ for better music discovery**

*Perfect for campus placements, CV projects, and music lovers!*
