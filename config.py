"""
Configuration file for the Emotion-Based Music Recommendation System
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Spotify API Configuration
SPOTIFY_CLIENT_ID = os.getenv('SPOTIFY_CLIENT_ID')
SPOTIFY_CLIENT_SECRET = os.getenv('SPOTIFY_CLIENT_SECRET')

# YouTube API Configuration (fallback)
YOUTUBE_API_KEY = os.getenv('YOUTUBE_API_KEY')

# Emotion Detection Configuration
EMOTION_LABELS = ['angry', 'disgust', 'fear', 'happy', 'sad', 'surprise', 'neutral']

# Music Recommendation Configuration
EMOTION_MUSIC_MAPPING = {
    'happy': ['upbeat', 'pop', 'dance', 'feel good', 'energetic'],
    'sad': ['melancholy', 'slow', 'emotional', 'ballad', 'blues'],
    'angry': ['rock', 'metal', 'aggressive', 'intense', 'punk'],
    'fear': ['calm', 'soothing', 'ambient', 'peaceful', 'relaxing'],
    'surprise': ['upbeat', 'exciting', 'dynamic', 'energetic'],
    'disgust': ['alternative', 'indie', 'experimental'],
    'neutral': ['chill', 'ambient', 'instrumental', 'lo-fi']
}

# Database Configuration
DATABASE_PATH = 'user_data.db'

# UI Configuration
EMOTION_EMOJIS = {
    'happy': '😊',
    'sad': '😢',
    'angry': '😠',
    'fear': '😨',
    'surprise': '😲',
    'disgust': '🤢',
    'neutral': '😐'
}

# App Configuration
APP_TITLE = "🎵 Enhanced Emotion-Based Music Recommender"
APP_DESCRIPTION = "AI-powered music recommendations based on real-time emotion detection"
