import streamlit as st
from streamlit_webrtc import webrtc_streamer
import av
import cv2
import numpy as np
import uuid
import logging
from datetime import datetime
from typing import Dict, List, Optional

# Import custom modules
from emotion_detector import EmotionDetector, FallbackEmotionDetector
from music_recommender import MusicRecommender
from user_database import UserDatabase
from config import APP_TITLE, APP_DESCRIPTION, EMOTION_EMOJIS

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configure Streamlit page
st.set_page_config(
    page_title="Enhanced Emotion Music Recommender",
    page_icon="🎵",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for enhanced UI
st.markdown("""
<style>
    .main-header {
        text-align: center;
        color: #1DB954;
        font-size: 3rem;
        font-weight: bold;
        margin-bottom: 1rem;
    }
    .emotion-display {
        background: linear-gradient(90deg, #1DB954, #1ed760);
        color: white;
        padding: 1rem;
        border-radius: 10px;
        text-align: center;
        font-size: 1.5rem;
        margin: 1rem 0;
    }
    .recommendation-card {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 10px;
        padding: 1rem;
        margin: 0.5rem 0;
    }
    .feedback-section {
        background: #e9ecef;
        padding: 1rem;
        border-radius: 10px;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

st.markdown(f'<h1 class="main-header">{APP_TITLE}</h1>', unsafe_allow_html=True)
st.markdown(f'<p style="text-align: center; font-size: 1.2rem; color: #666;">{APP_DESCRIPTION}</p>', unsafe_allow_html=True)

# Initialize session state
if "session_id" not in st.session_state:
    st.session_state.session_id = str(uuid.uuid4())
    st.session_state.emotion_detector = None
    st.session_state.music_recommender = None
    st.session_state.user_db = None
    st.session_state.current_emotion = "neutral"
    st.session_state.emotion_confidence = 0.0
    st.session_state.recommendations = []
    st.session_state.detection_active = False
    st.session_state.feedback_given = {}

# Initialize components
@st.cache_resource
def initialize_components():
    """Initialize emotion detector, music recommender, and database"""
    try:
        # Try to use DeepFace first, fallback to original model
        emotion_detector = EmotionDetector()
        logger.info("DeepFace emotion detector initialized")
    except Exception as e:
        logger.warning(f"DeepFace initialization failed, using fallback: {str(e)}")
        emotion_detector = FallbackEmotionDetector()

    music_recommender = MusicRecommender()
    user_db = UserDatabase()

    return emotion_detector, music_recommender, user_db

# Get initialized components
if st.session_state.emotion_detector is None:
    st.session_state.emotion_detector, st.session_state.music_recommender, st.session_state.user_db = initialize_components()

class EnhancedEmotionProcessor:
    """Enhanced emotion processor with real-time detection and UI updates"""

    def __init__(self, emotion_detector, user_db, session_id):
        self.emotion_detector = emotion_detector
        self.user_db = user_db
        self.session_id = session_id
        self.frame_count = 0
        self.detection_interval = 10  # Process every 10th frame for performance

    def recv(self, frame):
        frm = frame.to_ndarray(format="bgr24")
        frm = cv2.flip(frm, 1)

        # Process emotion detection every nth frame
        if self.frame_count % self.detection_interval == 0:
            try:
                emotion_data = self.emotion_detector.detect_emotion(frm)

                # Update session state
                st.session_state.current_emotion = emotion_data['emotion']
                st.session_state.emotion_confidence = emotion_data['confidence']

                # Log emotion detection
                self.user_db.log_emotion_detection(
                    self.session_id,
                    emotion_data['emotion'],
                    emotion_data['confidence']
                )

                # Draw emotion info on frame
                frm = self.emotion_detector.draw_emotion_info(frm, emotion_data)

            except Exception as e:
                logger.error(f"Emotion processing error: {str(e)}")
                # Draw error message
                cv2.putText(frm, "Emotion Detection Error", (50, 50),
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)

        self.frame_count += 1
        return av.VideoFrame.from_ndarray(frm, format="bgr24")

# Sidebar for user preferences
with st.sidebar:
    st.header("🎵 User Preferences")

    # User input fields
    language = st.text_input("Preferred Language", value="English", help="e.g., English, Spanish, Hindi")
    favorite_artist = st.text_input("Favorite Artist", help="e.g., Taylor Swift, Ed Sheeran")

    # Create user session
    if language or favorite_artist:
        st.session_state.user_db.create_session(
            st.session_state.session_id,
            language,
            favorite_artist
        )

    st.divider()

    # Current emotion display
    st.header("😊 Current Emotion")
    if st.session_state.current_emotion:
        emoji = EMOTION_EMOJIS.get(st.session_state.current_emotion, '😐')
        confidence_color = "green" if st.session_state.emotion_confidence > 0.7 else "orange" if st.session_state.emotion_confidence > 0.4 else "red"

        st.markdown(f"""
        <div class="emotion-display">
            <div style="font-size: 3rem;">{emoji}</div>
            <div style="font-size: 1.5rem; font-weight: bold;">{st.session_state.current_emotion.title()}</div>
            <div style="color: {confidence_color};">Confidence: {st.session_state.emotion_confidence:.2f}</div>
        </div>
        """, unsafe_allow_html=True)

    st.divider()

    # Detection controls
    st.header("📹 Emotion Detection")
    detection_mode = st.radio(
        "Detection Mode",
        ["Real-time", "On-demand"],
        help="Real-time: Continuous detection, On-demand: Click to detect"
    )

    if detection_mode == "Real-time":
        st.session_state.detection_active = True
    else:
        st.session_state.detection_active = False

# Main content area
col1, col2 = st.columns([2, 1])

with col1:
    st.header("📹 Live Emotion Detection")

    if st.session_state.detection_active or detection_mode == "Real-time":
        # Real-time emotion detection
        if language and favorite_artist:
            webrtc_streamer(
                key="emotion_detection",
                desired_playing_state=True,
                video_processor_factory=lambda: EnhancedEmotionProcessor(
                    st.session_state.emotion_detector,
                    st.session_state.user_db,
                    st.session_state.session_id
                ),
                media_stream_constraints={
                    "video": {"width": 640, "height": 480},
                    "audio": False
                }
            )
        else:
            st.warning("⚠️ Please enter your preferred language and favorite artist in the sidebar to start emotion detection.")

    else:
        st.info("📸 Click 'Detect Emotion' to capture your current emotion")
        if st.button("📸 Detect Emotion", type="primary"):
            # Placeholder for on-demand detection
            st.info("On-demand detection feature coming soon!")

with col2:
    st.header("🎵 Music Recommendations")

    # Get recommendations button
    if st.button("🎧 Get Music Recommendations", type="primary", use_container_width=True):
        if st.session_state.current_emotion and st.session_state.current_emotion != "neutral":
            with st.spinner("🎵 Finding perfect songs for your mood..."):
                try:
                    # Get recommendations
                    recommendations = st.session_state.music_recommender.get_recommendations(
                        emotion=st.session_state.current_emotion,
                        language=language,
                        artist=favorite_artist,
                        limit=5
                    )

                    st.session_state.recommendations = recommendations

                    # Log recommendations
                    for rec in recommendations:
                        rec_id = st.session_state.user_db.log_music_recommendation(
                            st.session_state.session_id,
                            st.session_state.current_emotion,
                            rec['name'],
                            rec['artist'],
                            rec['source']
                        )
                        rec['recommendation_id'] = rec_id

                    st.success(f"🎵 Found {len(recommendations)} songs for your {st.session_state.current_emotion} mood!")

                except Exception as e:
                    st.error(f"❌ Failed to get recommendations: {str(e)}")
                    logger.error(f"Recommendation error: {str(e)}")
        else:
            st.warning("⚠️ Please let me detect your emotion first!")

    # Display recommendations
    if st.session_state.recommendations:
        st.subheader(f"🎵 Songs for your {st.session_state.current_emotion} mood:")

        for i, rec in enumerate(st.session_state.recommendations):
            with st.container():
                st.markdown(f"""
                <div class="recommendation-card">
                    <h4>🎵 {rec['name']}</h4>
                    <p><strong>Artist:</strong> {rec['artist']}</p>
                    <p><strong>Source:</strong> {rec['source'].title()}</p>
                </div>
                """, unsafe_allow_html=True)

                # Action buttons
                col_play, col_feedback = st.columns(2)

                with col_play:
                    if rec.get('external_url'):
                        if st.button(f"🎵 Play on Spotify", key=f"play_{i}"):
                            st.markdown(f"[Open in Spotify]({rec['external_url']})")
                    elif rec.get('youtube_url'):
                        if st.button(f"▶️ Search on YouTube", key=f"youtube_{i}"):
                            st.markdown(f"[Search on YouTube]({rec['youtube_url']})")

                with col_feedback:
                    if st.button(f"💬 Give Feedback", key=f"feedback_{i}"):
                        st.session_state[f"show_feedback_{i}"] = True

                # Feedback form
                if st.session_state.get(f"show_feedback_{i}", False):
                    with st.expander("📝 Rate this recommendation", expanded=True):
                        rating = st.slider(
                            "How much did you like this song?",
                            1, 5, 3,
                            key=f"rating_{i}",
                            help="1 = Hate it, 5 = Love it"
                        )

                        feedback_text = st.text_area(
                            "Additional feedback (optional)",
                            key=f"feedback_text_{i}",
                            placeholder="Tell us what you think..."
                        )

                        if st.button(f"Submit Feedback", key=f"submit_feedback_{i}"):
                            # Save feedback
                            success = st.session_state.user_db.add_user_feedback(
                                st.session_state.session_id,
                                rec.get('recommendation_id', 0),
                                st.session_state.current_emotion,
                                rec['name'],
                                rec['artist'],
                                rating,
                                feedback_text
                            )

                            if success:
                                st.success("✅ Thank you for your feedback!")
                                st.session_state[f"show_feedback_{i}"] = False
                                st.rerun()
                            else:
                                st.error("❌ Failed to save feedback")

                st.divider()

# Analytics Dashboard
st.header("📊 Analytics Dashboard")

# Create tabs for different analytics views
tab1, tab2, tab3 = st.tabs(["📈 Emotion Statistics", "🎵 Music Preferences", "📝 Feedback Summary"])

with tab1:
    st.subheader("📈 Your Emotion Patterns")

    # Get emotion statistics
    emotion_stats = st.session_state.user_db.get_emotion_statistics(st.session_state.session_id)

    if emotion_stats['total_detections'] > 0:
        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("Total Detections", emotion_stats['total_detections'])

        with col2:
            st.metric("Most Common Emotion", emotion_stats['most_common_emotion'].title())

        with col3:
            most_common_emoji = EMOTION_EMOJIS.get(emotion_stats['most_common_emotion'], '😐')
            st.metric("Dominant Mood", most_common_emoji)

        # Emotion breakdown chart
        if emotion_stats['emotion_breakdown']:
            import plotly.express as px
            import pandas as pd

            # Prepare data for chart
            emotion_data = pd.DataFrame(emotion_stats['emotion_breakdown'])

            # Create pie chart
            fig = px.pie(
                emotion_data,
                values='count',
                names='emotion',
                title="Emotion Distribution",
                color_discrete_sequence=px.colors.qualitative.Set3
            )

            st.plotly_chart(fig, use_container_width=True)

            # Create bar chart for confidence levels
            fig_bar = px.bar(
                emotion_data,
                x='emotion',
                y='avg_confidence',
                title="Average Confidence by Emotion",
                color='avg_confidence',
                color_continuous_scale='Viridis'
            )

            st.plotly_chart(fig_bar, use_container_width=True)
    else:
        st.info("📊 Start using the emotion detection to see your analytics!")

with tab2:
    st.subheader("🎵 Your Music Journey")

    # Get user preferences
    if st.session_state.current_emotion:
        preferences = st.session_state.user_db.get_user_preferences(
            st.session_state.session_id,
            st.session_state.current_emotion
        )

        if preferences['preferred_artists']:
            st.write("**Your Favorite Artists:**")
            for artist in preferences['preferred_artists']:
                st.write(f"🎤 {artist}")

        if preferences['preferred_genres']:
            st.write("**Your Preferred Genres:**")
            for genre in preferences['preferred_genres']:
                st.write(f"🎵 {genre}")

    # Recommendation history (placeholder)
    st.info("🎵 Music recommendation history will appear here as you use the app!")

with tab3:
    st.subheader("📝 Your Feedback Summary")

    # Get feedback statistics
    feedback_stats = st.session_state.user_db.get_feedback_statistics(st.session_state.session_id)

    if feedback_stats['overall_satisfaction'] > 0:
        col1, col2 = st.columns(2)

        with col1:
            st.metric("Overall Satisfaction", f"{feedback_stats['overall_satisfaction']:.1f}/5.0")

        with col2:
            satisfaction_emoji = "😍" if feedback_stats['overall_satisfaction'] >= 4 else "😊" if feedback_stats['overall_satisfaction'] >= 3 else "😐"
            st.metric("Satisfaction Level", satisfaction_emoji)

        # Feedback by emotion
        if feedback_stats['emotion_feedback']:
            import pandas as pd
            import plotly.express as px

            feedback_data = pd.DataFrame(feedback_stats['emotion_feedback'])

            fig = px.bar(
                feedback_data,
                x='emotion',
                y='avg_rating',
                title="Average Rating by Emotion",
                color='avg_rating',
                color_continuous_scale='RdYlGn',
                range_y=[1, 5]
            )

            st.plotly_chart(fig, use_container_width=True)
    else:
        st.info("📝 Give feedback on recommendations to see your satisfaction trends!")

# Footer
st.divider()
st.markdown("""
<div style="text-align: center; color: #666; padding: 2rem;">
    <p>🎵 Enhanced Emotion-Based Music Recommender</p>
    <p>Powered by DeepFace AI, Spotify API, and Streamlit</p>
    <p>Made with ❤️ for better music discovery</p>
</div>
""", unsafe_allow_html=True)
