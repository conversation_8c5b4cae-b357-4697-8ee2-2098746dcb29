"""
Tests for music recommendation functionality
"""
import pytest
from unittest.mock import Mock, patch
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from music_recommender import MusicRecommender

class TestMusicRecommender:
    """Test cases for the music recommender"""
    
    def setup_method(self):
        """Setup test fixtures"""
        with patch('music_recommender.spotipy.Spotify'), \
             patch('music_recommender.SpotifyClientCredentials'):
            self.recommender = MusicRecommender()
    
    def test_initialization_without_credentials(self):
        """Test initialization when Spotify credentials are not available"""
        with patch('music_recommender.SPOTIFY_CLIENT_ID', None), \
             patch('music_recommender.SPOTIFY_CLIENT_SECRET', None):
            recommender = MusicRecommender()
            assert recommender.spotify is None
    
    @patch('music_recommender.SPOTIFY_CLIENT_ID', 'test_id')
    @patch('music_recommender.SPOTIFY_CLIENT_SECRET', 'test_secret')
    def test_initialization_with_credentials(self):
        """Test initialization with valid Spotify credentials"""
        with patch('music_recommender.spotipy.Spotify') as mock_spotify, \
             patch('music_recommender.SpotifyClientCredentials'):
            recommender = MusicRecommender()
            assert recommender.spotify is not None
    
    def test_get_recommendations_fallback(self):
        """Test getting recommendations using fallback playlists"""
        self.recommender.spotify = None
        
        recommendations = self.recommender.get_recommendations(
            emotion='happy',
            language='English',
            artist='',
            limit=3
        )
        
        assert len(recommendations) <= 3
        assert all('name' in rec for rec in recommendations)
        assert all('artist' in rec for rec in recommendations)
        assert all('source' in rec for rec in recommendations)
        assert all(rec['source'] == 'fallback' for rec in recommendations)
    
    def test_get_spotify_recommendations(self):
        """Test getting recommendations from Spotify API"""
        # Mock Spotify API response
        mock_track = {
            'name': 'Happy Song',
            'artists': [{'name': 'Happy Artist'}],
            'album': {
                'name': 'Happy Album',
                'images': [{'url': 'http://example.com/image.jpg'}]
            },
            'duration_ms': 180000,
            'preview_url': 'http://example.com/preview.mp3',
            'external_urls': {'spotify': 'http://spotify.com/track'},
            'popularity': 80
        }
        
        mock_search_result = {
            'tracks': {
                'items': [mock_track]
            }
        }
        
        self.recommender.spotify = Mock()
        self.recommender.spotify.search.return_value = mock_search_result
        
        recommendations = self.recommender._get_spotify_recommendations(
            emotion='happy',
            language='English',
            artist='Test Artist',
            limit=5
        )
        
        assert len(recommendations) == 1
        assert recommendations[0]['name'] == 'Happy Song'
        assert recommendations[0]['artist'] == 'Happy Artist'
        assert recommendations[0]['source'] == 'spotify'
        assert 'external_url' in recommendations[0]
    
    def test_get_fallback_recommendations_with_artist_filter(self):
        """Test fallback recommendations with artist filtering"""
        self.recommender.spotify = None
        
        recommendations = self.recommender._get_fallback_recommendations(
            emotion='happy',
            language='English',
            artist='Pharrell',
            limit=5
        )
        
        # Should find Pharrell Williams in happy playlist
        assert len(recommendations) > 0
        assert any('Pharrell' in rec['artist'] for rec in recommendations)
    
    def test_get_playlist_by_emotion(self):
        """Test getting Spotify playlist by emotion"""
        mock_playlist = {
            'external_urls': {'spotify': 'http://spotify.com/playlist'}
        }
        
        mock_search_result = {
            'playlists': {
                'items': [mock_playlist]
            }
        }
        
        self.recommender.spotify = Mock()
        self.recommender.spotify.search.return_value = mock_search_result
        
        playlist_url = self.recommender.get_playlist_by_emotion('happy')
        
        assert playlist_url == 'http://spotify.com/playlist'
    
    def test_get_playlist_by_emotion_no_results(self):
        """Test getting playlist when no results found"""
        mock_search_result = {
            'playlists': {
                'items': []
            }
        }
        
        self.recommender.spotify = Mock()
        self.recommender.spotify.search.return_value = mock_search_result
        
        playlist_url = self.recommender.get_playlist_by_emotion('happy')
        
        assert playlist_url is None
    
    def test_search_youtube_music(self):
        """Test YouTube music search URL generation"""
        url = self.recommender.search_youtube_music(
            emotion='happy',
            language='English',
            artist='Test Artist'
        )
        
        assert 'youtube.com/results' in url
        assert 'search_query=' in url
        assert 'English' in url
        assert 'happy' in url
        assert 'Test+Artist' in url
    
    def test_fallback_playlists_structure(self):
        """Test that fallback playlists have correct structure"""
        playlists = self.recommender.fallback_playlists
        
        # Check that all emotions have playlists
        expected_emotions = ['happy', 'sad', 'angry', 'fear', 'surprise', 'disgust', 'neutral']
        for emotion in expected_emotions:
            assert emotion in playlists
            assert len(playlists[emotion]) > 0
            
            # Check playlist structure
            for track in playlists[emotion]:
                assert 'name' in track
                assert 'artist' in track
                assert 'album' in track
    
    def test_emotion_music_mapping_coverage(self):
        """Test that emotion music mapping covers all emotions"""
        from config import EMOTION_MUSIC_MAPPING, EMOTION_LABELS
        
        for emotion in EMOTION_LABELS:
            assert emotion in EMOTION_MUSIC_MAPPING
            assert len(EMOTION_MUSIC_MAPPING[emotion]) > 0

if __name__ == '__main__':
    pytest.main([__file__])
