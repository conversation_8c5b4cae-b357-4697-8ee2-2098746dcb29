"""
Enhanced Emotion Detection Module using DeepFace
Supports 7 emotions: angry, disgust, fear, happy, sad, surprise, neutral
"""
import cv2
import numpy as np
from deepface import DeepFace
import logging
from typing import Dict, Tuple, Optional
from config import EMOTION_LABELS, EMOTION_EMOJIS

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EmotionDetector:
    """Enhanced emotion detector using DeepFace for better accuracy"""
    
    def __init__(self):
        self.emotion_history = []
        self.confidence_threshold = 0.3
        self.history_size = 5
        
    def detect_emotion(self, frame: np.ndarray) -> Dict[str, any]:
        """
        Detect emotion from a video frame using DeepFace
        
        Args:
            frame: Input video frame (BGR format)
            
        Returns:
            Dictionary containing emotion, confidence, and additional info
        """
        try:
            # Convert BGR to RGB for DeepFace
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # Analyze emotion using DeepFace
            result = DeepFace.analyze(
                img_path=rgb_frame,
                actions=['emotion'],
                enforce_detection=False,
                silent=True
            )
            
            # Handle both single face and multiple faces
            if isinstance(result, list):
                result = result[0]  # Take the first face
            
            emotions = result['emotion']
            dominant_emotion = result['dominant_emotion']
            confidence = emotions[dominant_emotion] / 100.0
            
            # Apply confidence threshold
            if confidence < self.confidence_threshold:
                dominant_emotion = 'neutral'
                confidence = 0.5
            
            # Update emotion history for smoothing
            self._update_history(dominant_emotion, confidence)
            
            # Get smoothed emotion
            smoothed_emotion = self._get_smoothed_emotion()
            
            return {
                'emotion': smoothed_emotion,
                'confidence': confidence,
                'all_emotions': emotions,
                'raw_emotion': dominant_emotion,
                'emoji': EMOTION_EMOJIS.get(smoothed_emotion, '😐'),
                'timestamp': cv2.getTickCount()
            }
            
        except Exception as e:
            logger.warning(f"Emotion detection failed: {str(e)}")
            return {
                'emotion': 'neutral',
                'confidence': 0.0,
                'all_emotions': {},
                'raw_emotion': 'neutral',
                'emoji': '😐',
                'timestamp': cv2.getTickCount(),
                'error': str(e)
            }
    
    def _update_history(self, emotion: str, confidence: float):
        """Update emotion history for smoothing"""
        self.emotion_history.append({
            'emotion': emotion,
            'confidence': confidence,
            'timestamp': cv2.getTickCount()
        })
        
        # Keep only recent history
        if len(self.emotion_history) > self.history_size:
            self.emotion_history.pop(0)
    
    def _get_smoothed_emotion(self) -> str:
        """Get smoothed emotion based on recent history"""
        if not self.emotion_history:
            return 'neutral'
        
        # Weight recent emotions more heavily
        emotion_scores = {}
        total_weight = 0
        
        for i, entry in enumerate(self.emotion_history):
            weight = (i + 1) * entry['confidence']  # More recent = higher weight
            emotion = entry['emotion']
            
            if emotion not in emotion_scores:
                emotion_scores[emotion] = 0
            emotion_scores[emotion] += weight
            total_weight += weight
        
        if total_weight == 0:
            return 'neutral'
        
        # Normalize scores
        for emotion in emotion_scores:
            emotion_scores[emotion] /= total_weight
        
        # Return emotion with highest score
        return max(emotion_scores, key=emotion_scores.get)
    
    def draw_emotion_info(self, frame: np.ndarray, emotion_data: Dict) -> np.ndarray:
        """Draw emotion information on the frame"""
        height, width = frame.shape[:2]
        
        # Draw emotion text
        emotion_text = f"{emotion_data['emoji']} {emotion_data['emotion'].title()}"
        confidence_text = f"Confidence: {emotion_data['confidence']:.2f}"
        
        # Background rectangle for text
        cv2.rectangle(frame, (10, 10), (400, 80), (0, 0, 0), -1)
        cv2.rectangle(frame, (10, 10), (400, 80), (255, 255, 255), 2)
        
        # Draw text
        cv2.putText(frame, emotion_text, (20, 35), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        cv2.putText(frame, confidence_text, (20, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 1)
        
        return frame
    
    def get_emotion_statistics(self) -> Dict:
        """Get statistics about detected emotions"""
        if not self.emotion_history:
            return {}
        
        emotion_counts = {}
        for entry in self.emotion_history:
            emotion = entry['emotion']
            emotion_counts[emotion] = emotion_counts.get(emotion, 0) + 1
        
        total_detections = len(self.emotion_history)
        emotion_percentages = {
            emotion: (count / total_detections) * 100 
            for emotion, count in emotion_counts.items()
        }
        
        return {
            'total_detections': total_detections,
            'emotion_counts': emotion_counts,
            'emotion_percentages': emotion_percentages,
            'most_common': max(emotion_counts, key=emotion_counts.get) if emotion_counts else 'neutral'
        }

# Fallback emotion detector using the original MediaPipe approach
class FallbackEmotionDetector:
    """Fallback emotion detector using the original model"""
    
    def __init__(self, model_path: str = "model.h5", labels_path: str = "labels.npy"):
        try:
            from keras.models import load_model
            import mediapipe as mp
            
            self.model = load_model(model_path)
            self.labels = np.load(labels_path)
            
            self.holistic = mp.solutions.holistic
            self.hands = mp.solutions.hands
            self.holis = self.holistic.Holistic()
            self.drawing = mp.solutions.drawing_utils
            
            logger.info("Fallback emotion detector initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize fallback detector: {str(e)}")
            self.model = None
    
    def detect_emotion(self, frame: np.ndarray) -> Dict[str, any]:
        """Detect emotion using the original MediaPipe approach"""
        if self.model is None:
            return {
                'emotion': 'neutral',
                'confidence': 0.0,
                'emoji': '😐',
                'error': 'Model not loaded'
            }
        
        try:
            # Process frame with MediaPipe
            res = self.holis.process(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
            
            if res.face_landmarks:
                # Extract features (same as original implementation)
                lst = []
                
                for i in res.face_landmarks.landmark:
                    lst.append(i.x - res.face_landmarks.landmark[1].x)
                    lst.append(i.y - res.face_landmarks.landmark[1].y)
                
                # Add hand landmarks
                if res.left_hand_landmarks:
                    for i in res.left_hand_landmarks.landmark:
                        lst.append(i.x - res.left_hand_landmarks.landmark[8].x)
                        lst.append(i.y - res.left_hand_landmarks.landmark[8].y)
                else:
                    for i in range(42):
                        lst.append(0.0)
                
                if res.right_hand_landmarks:
                    for i in res.right_hand_landmarks.landmark:
                        lst.append(i.x - res.right_hand_landmarks.landmark[8].x)
                        lst.append(i.y - res.right_hand_landmarks.landmark[8].y)
                else:
                    for i in range(42):
                        lst.append(0.0)
                
                # Predict emotion
                lst = np.array(lst).reshape(1, -1)
                prediction = self.model.predict(lst, verbose=0)
                emotion_idx = np.argmax(prediction)
                emotion = self.labels[emotion_idx]
                confidence = float(np.max(prediction))
                
                return {
                    'emotion': emotion,
                    'confidence': confidence,
                    'emoji': EMOTION_EMOJIS.get(emotion, '😐'),
                    'timestamp': cv2.getTickCount()
                }
            
            return {
                'emotion': 'neutral',
                'confidence': 0.0,
                'emoji': '😐',
                'timestamp': cv2.getTickCount()
            }
            
        except Exception as e:
            logger.error(f"Fallback emotion detection failed: {str(e)}")
            return {
                'emotion': 'neutral',
                'confidence': 0.0,
                'emoji': '😐',
                'error': str(e)
            }
